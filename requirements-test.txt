# Build tools and compatibility - MUST be installed first
setuptools<60.0
wheel>=0.37.0
Cython<3.0.0

# Core data processing libraries - use versions with reliable wheels for Alpine/musllinux
numpy==1.26.4
pandas==2.1.4

# PySpark for testing (compatible with EMR Spark 3.5.4)
pyspark==3.5.4

# Testing dependencies - works for both local and CI environments
pytest==7.4.3
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-xdist==3.3.1

# Mock and testing utilities
mock==5.1.0

# Date utilities
python-dateutil==2.8.2
pytz==2023.3

# JSON logging (if needed for tests)
json-logging==1.3.0

# Redis (for mocking)
redis==5.0.1
