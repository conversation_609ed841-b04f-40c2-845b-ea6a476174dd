# FlashGamesPnL Unit Tests

This document provides comprehensive instructions for running the unit tests for the `FlashGamesPnL` class.

## Overview

The test suite includes:
- **Unit Tests**: Test individual methods and components in isolation
- **Integration Tests**: Test the complete execution flow with realistic data
- **Mock Data**: Comprehensive mock data generators for all scenarios
- **Coverage Reporting**: Code coverage analysis and reporting

## Test Structure

```
tests/
├── conftest.py                          # Pytest configuration and fixtures
├── test_flash_games_pnl.py             # Unit tests for individual methods
├── test_flash_games_pnl_integration.py # Integration tests for full flow
└── mock_data.py                         # Mock data generators (if needed)

requirements-test.txt                    # Test dependencies
pytest.ini                              # Pytest configuration
run_tests.py                            # Test runner script
```

## Prerequisites

1. **Python 3.8+** installed
2. **Java 11 or 17** (required for PySpark 3.3.4+)
   - **Important**: PySpark 3.3.4 requires Java 11 minimum
   - If you have Java 8, you'll need to upgrade or use an older PySpark version
3. **Git** (for cloning the repository)

### Java Version Compatibility

- **PySpark 3.3.4**: Requires Java 11+
- **PySpark 3.2.x**: Compatible with Java 8+

If you encounter Java version issues, you have two options:
1. **Upgrade Java** (recommended): Install Java 11 or 17
2. **Use older PySpark**: Modify `requirements-test.txt` to use `pyspark==3.2.4`

## Installation

### 1. Install Test Dependencies

```bash
# Install test dependencies
pip install -r requirements-test.txt

# Or use the test runner to install dependencies
python run_tests.py --install-deps
```

### 2. Set Environment Variables

```bash
# Set JAVA_HOME (adjust path as needed)
export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64

# Set SPARK_LOCAL_IP (for local testing)
export SPARK_LOCAL_IP=127.0.0.1

# Add src to Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
```

## Running Tests

### Quick Start (No Spark Required)

If you want to quickly test the core logic without setting up Spark:

```bash
# Run simple tests that don't require Spark
python run_simple_tests.py
```

This will test the core PnL calculation logic and other non-Spark functionality.

### Option 1: Using the Test Runner Script (Recommended)

The `run_tests.py` script provides various options for running tests:

```bash
# Run all tests
python run_tests.py

# Run only unit tests
python run_tests.py --unit

# Run only integration tests
python run_tests.py --integration

# Run tests with coverage reporting
python run_tests.py --coverage

# Run tests in parallel (faster)
python run_tests.py --parallel

# Run a specific test file
python run_tests.py --test test_flash_games_pnl.py

# Install dependencies and run all tests
python run_tests.py --install-deps --coverage
```

### Option 2: Using Pytest Directly

```bash
# Run all tests
pytest tests/ -v

# Run unit tests only
pytest tests/test_flash_games_pnl.py -v

# Run integration tests only
pytest tests/test_flash_games_pnl_integration.py -v

# Run with coverage
pytest tests/ -v --cov=src --cov-report=html --cov-report=term-missing

# Run specific test method
pytest tests/test_flash_games_pnl.py::TestFlashGamesPnL::test_init -v

# Run tests matching a pattern
pytest tests/ -k "test_get_current_flash_game" -v
```

## Test Categories

### Unit Tests (`test_flash_games_pnl.py`)

Tests individual methods of the `FlashGamesPnL` class:

- `test_init`: Constructor initialization
- `test_get_current_flash_game_active`: Active flash game detection
- `test_get_current_flash_game_none_active`: No active game scenario
- `test_get_live_options_contracts_data`: Options contracts data retrieval
- `test_get_global_stocks_leverage_data`: Leverage stocks data retrieval
- `test_get_all_eligible_transactions`: Transaction filtering logic
- `test_create_batches_static_method`: PnL calculation logic
- `test_cast_fields`: Field casting and rounding
- `test_process_flash_game_pnl`: PnL processing and ranking
- `test_write_flash_game_pnl_to_mongo`: MongoDB writing
- `test_execute_no_active_game`: Graceful exit when no game active
- `test_run_method`: Entry point method
- `test_create_initial_position`: Initial position calculation
- `test_get_current_flash_game_asset_id_with_options_and_leverage`: Asset ID collection
- `test_edge_case_empty_transactions`: Empty data handling

### Integration Tests (`test_flash_games_pnl_integration.py`)

Tests the complete execution flow:

- `test_full_execution_flow`: Complete end-to-end execution
- `test_run_method_integration`: Run method with Spark session management
- `test_complex_pnl_calculation_scenarios`: Complex PnL calculations

## Test Data

The tests use comprehensive mock data including:

- **Transaction Data**: Buy/sell transactions across different asset types
- **Asset Data**: Global stocks, crypto currencies, options contracts
- **User Data**: NIV, GTV, and user details
- **Configuration Data**: Flash game configurations and trading competition settings

## Coverage Reporting

After running tests with coverage, you can view the reports:

```bash
# View coverage in terminal
pytest tests/ --cov=src --cov-report=term-missing

# Generate HTML coverage report
pytest tests/ --cov=src --cov-report=html

# Open HTML report in browser
open htmlcov/index.html  # macOS
xdg-open htmlcov/index.html  # Linux
```

## Troubleshooting

### Common Issues

1. **Java Version Compatibility Error**
   ```
   Error: java.lang.UnsupportedClassVersionError: ... has been compiled by a more recent version of the Java Runtime
   ```
   **Solution**: Upgrade to Java 11+ or use older PySpark version:
   ```bash
   # Check Java version
   java -version

   # If Java 8, either upgrade Java or modify requirements-test.txt:
   # Change pyspark==3.3.4 to pyspark==3.2.4
   ```

2. **Java Not Found**
   ```bash
   export JAVA_HOME=/path/to/your/java
   # On macOS with Homebrew: export JAVA_HOME=$(/usr/libexec/java_home -v 11)
   # On Ubuntu: export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
   ```

3. **PySpark Issues**
   ```bash
   export SPARK_LOCAL_IP=127.0.0.1
   export PYSPARK_PYTHON=python3
   ```

4. **Import Errors**
   ```bash
   export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
   ```

5. **Permission Issues**
   ```bash
   chmod +x run_tests.py
   chmod +x run_simple_tests.py
   ```

### Debug Mode

Run tests with more verbose output:

```bash
# Maximum verbosity
pytest tests/ -vvv --tb=long

# Show print statements
pytest tests/ -v -s

# Stop on first failure
pytest tests/ -v -x
```

## Java 17 Compatibility

If you're using Java 17, you may encounter compatibility issues with PySpark. The test configuration has been updated to handle Java 17 compatibility automatically:

- **Jenkins CI**: Java 17 compatibility options and JAVA_HOME are automatically set in the pipeline environment
- **Local testing**: The Spark session configuration in `conftest.py` includes the necessary JVM arguments and attempts to locate Java automatically

### Requirements

- **Java 17** must be installed
- **JAVA_HOME** environment variable should be set (the test configuration will attempt to find Java automatically if not set)

### Manual Setup

For manual local testing with Java 17:

```bash
# Set JAVA_HOME if not already set
export JAVA_HOME="/usr/lib/jvm/java-17-openjdk-amd64"  # or your Java installation path

# Optional: Set Java 17 compatibility options
export JAVA_TOOL_OPTIONS="--add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED"

# Run tests
python -m pytest tests/ -v
```

The full list of Java 17 compatibility options is already configured in both the Jenkins pipeline and the test Spark session.

## Continuous Integration

For CI/CD pipelines, use:

```bash
# Install dependencies and run tests with coverage
python run_tests.py --install-deps --coverage

# Or with pytest directly
pip install -r requirements-test.txt
pytest tests/ --cov=src --cov-report=xml --cov-fail-under=80
```

## Test Configuration

The `pytest.ini` file contains default configuration:

- Test discovery patterns
- Coverage settings
- Warning filters
- Markers for test categorization

## TransactionTransformer Tests

### Overview

The TransactionTransformer test suite provides comprehensive coverage for the transaction processing functionality:

### Test Files

1. **`test_transaction_transformer.py`** - Unit tests with Spark session
2. **`test_transaction_transformer_integration.py`** - Integration tests
3. **`test_transaction_transformer_no_spark.py`** - Unit tests without Spark dependencies

### Running TransactionTransformer Tests

```bash
# Run all TransactionTransformer tests
python run_tests.py --transaction-transformer

# Run specific test files
pytest tests/test_transaction_transformer_no_spark.py -v
pytest tests/test_transaction_transformer.py::TestTransactionTransformer::test_init -v
pytest tests/test_transaction_transformer.py::TestTransactionTransformer::test_run -v
```

### Test Coverage

The TransactionTransformer tests cover:

- **Initialization**: Configuration setup and validation
- **Price Retrieval**: All asset types (crypto, stocks, forex, gold, funds, options)
- **Transaction Processing**: Different transaction types and asset classes
- **Data Transformations**: Field casting, stock splits, price additions
- **Integration**: End-to-end execution flow
- **Error Handling**: Edge cases and error scenarios

### Test Structure

- **No-Spark Tests**: Fast tests for core logic without Spark dependencies
- **Spark Tests**: Tests requiring Spark session (initialization and run methods)
- **Integration Tests**: Full workflow testing with realistic data

## Adding New Tests

When adding new tests:

1. Follow the existing naming convention (`test_*`)
2. Use appropriate fixtures from `conftest.py`
3. Add integration tests for new major features
4. Update this README if needed

## Performance

- **Unit tests**: ~30-60 seconds
- **Integration tests**: ~60-120 seconds
- **All tests with coverage**: ~90-180 seconds

Use `--parallel` flag to run tests faster on multi-core systems.

## Support

If you encounter issues:

1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Check that environment variables are set correctly
4. Review the test output for specific error messages
