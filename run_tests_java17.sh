#!/bin/bash

# <PERSON><PERSON><PERSON> to run tests with Java 17 compatibility options
# This script sets the necessary JVM arguments for PySpark to work with Java 17

set -e

echo "🔧 Setting up Java 17 compatibility for PySpark tests..."

# Java 17 compatibility options
export JAVA_TOOL_OPTIONS="--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED"

# Test environment variables
export SPARK_LOCAL_IP=127.0.0.1
export PYTHONPATH="$(pwd)/src"
export ENABLE_JSON_LOGGING=false

echo "📋 Java options set: $JAVA_TOOL_OPTIONS"
echo "🌐 SPARK_LOCAL_IP: $SPARK_LOCAL_IP"
echo "📁 PYTHONPATH: $PYTHONPATH"

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "❌ Virtual environment not found. Please run:"
    echo "   python3 -m venv .venv"
    echo "   source .venv/bin/activate"
    echo "   ./install_test_deps.sh"
    exit 1
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source .venv/bin/activate

# Check if pytest is installed
if ! python -c "import pytest" 2>/dev/null; then
    echo "❌ pytest not found. Installing test dependencies..."
    ./install_test_deps.sh
fi

# Run tests based on arguments
if [ "$1" = "--all" ]; then
    echo "🧪 Running all tests..."
    python -m pytest tests/ -v --tb=short
elif [ "$1" = "--coverage" ]; then
    echo "🧪 Running tests with coverage..."
    python -m pytest tests/ -v --tb=short --cov=src --cov-report=html:htmlcov --cov-report=term-missing
elif [ "$1" = "--unit" ]; then
    echo "🧪 Running unit tests only..."
    python -m pytest tests/ -v --tb=short -m "not integration"
elif [ "$1" = "--integration" ]; then
    echo "🧪 Running integration tests only..."
    python -m pytest tests/ -v --tb=short -m "integration"
elif [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --all         Run all tests"
    echo "  --coverage    Run tests with coverage reporting"
    echo "  --unit        Run unit tests only"
    echo "  --integration Run integration tests only"
    echo "  --help, -h    Show this help message"
    echo ""
    echo "If no option is provided, runs all tests by default."
else
    echo "🧪 Running all tests (default)..."
    python -m pytest tests/ -v --tb=short
fi

echo "✅ Tests completed!"
