from src.utils.spark_utils import *
from src.utils.redis_utils import *
import time

class RedisWrite:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        self.config = config
        self.max_retries = 4
        self.retry_delay = 2  # seconds
        self.ttl = 46800  # 7 hours

        # get utility objects
        self.spark_utils = SparkUtils("trading_competition_redis_write")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.redis_utils = RedisUtils(self.config, self.max_retries, self.retry_delay, self.ttl)

        self.bucket_path = self.config.get("bucket_path")

        # Handling Dates & Timestamps
        self.utc_cutoff_ts = self.config.get("utc_cutoff_ts") or DateUtils.get_utc_timestamp()
        self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2 = DateUtils.get_tc_dates_and_timestamp(
            self.utc_cutoff_ts, self.config)
        self.logger.info("utc_cutoff_ts: {}, t_1: {}, h_1: {}, t_2: {}, h_2: {}, dt_1: {}, dt_2: {}".format(
            self.utc_cutoff_ts, self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2
        ))

        self.redis_write_keys = ["tc_forex_price", "tc_leader_board_update_datetime"]
        self.redis_delete_keys = ["tc_weekly_leader_board_data", "tc_leader_board_data:CHALLENGER", "tc_leader_board_data:LEGEND"]

    def get_forex_price(self):
        """
        Fetches the mid_price for a specific forex and partner ID.

        Returns:
            int: The mid_price value.

        Raises:
            ValueError: If the data is empty or the required key is missing.
            FileNotFoundError: If the input path is incorrect or inaccessible.
            Exception: For other unexpected issues.
        """
        try:
            # Read JSON data from S3
            input_path = "{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config["prices"]["forex"]["price_path"], self.t_1, self.h_1)
            forex_partner_price = self.io_utils.read_json_data(input_path)

            # Filter data
            forex_partner_price = forex_partner_price.filter(
                (col("forex_id") == 10000) & (col("partner_id") == self.config["pluang_partner_id"])
            )

            # Collect the filtered data
            collected_data = forex_partner_price.collect()

            # Check if data exists
            if not collected_data:
                raise ValueError(f'No data found for forex_id=10000 and partner_id={self.config["pluang_partner_id"]}.')

            # Extract mid_price
            mid_price = collected_data[0]["mid_price"]
            if mid_price is None:
                raise ValueError("The 'mid_price' key is missing in the data.")

            return int(mid_price)

        except AnalysisException as ae:
            raise FileNotFoundError(f"Failed to read input path {input_path}. Error: {ae}") from ae

        except ValueError as ve:
            raise ve  # Re-raise the ValueError with the same message

        except Exception as e:
            raise Exception(f"An unexpected error occurred: {e}") from e

    def start_processing(self):
        forex_price = self.get_forex_price()
        self.redis_utils.update_redis_key({self.redis_write_keys[0]: forex_price, self.redis_write_keys[1]: self.dt_1})
        self.redis_utils.delete_redis_keys(self.redis_delete_keys)

    def run(self):
        self.start_processing()
        self.spark_utils.stop_spark(self.spark)