# Java 17 Compatibility for PySpark Tests

## Problem

When running PySpark tests with Java 17, you may encounter the following error:

```
java.lang.IllegalAccessError: class org.apache.spark.storage.StorageUtils$ (in unnamed module @0xf381794) cannot access class sun.nio.ch.DirectBuffer (in module java.base) because module java.base does not export sun.nio.ch to unnamed module @0xf381794
```

This error occurs because Java 17 has stricter module access controls compared to earlier Java versions. PySpark 3.2.4 tries to access internal Java classes that are no longer accessible by default in Java 17.

## Solution

To fix this issue, we need to add specific JVM arguments that allow PySpark to access the required internal Java classes. These arguments use the `--add-opens` flag to open specific Java modules to PySpark.

### In Code

The `conftest.py` file has been updated to include these Java 17 compatibility options in the Spark session configuration:

```python
java_17_options = [
    "--add-opens=java.base/java.lang=ALL-UNNAMED",
    "--add-opens=java.base/java.lang.invoke=ALL-UNNAMED",
    "--add-opens=java.base/java.lang.reflect=ALL-UNNAMED",
    "--add-opens=java.base/java.io=ALL-UNNAMED",
    "--add-opens=java.base/java.net=ALL-UNNAMED",
    "--add-opens=java.base/java.nio=ALL-UNNAMED",
    "--add-opens=java.base/java.util=ALL-UNNAMED",
    "--add-opens=java.base/java.util.concurrent=ALL-UNNAMED",
    "--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED",
    "--add-opens=java.base/sun.nio.ch=ALL-UNNAMED",
    "--add-opens=java.base/sun.nio.cs=ALL-UNNAMED",
    "--add-opens=java.base/sun.security.action=ALL-UNNAMED",
    "--add-opens=java.base/sun.util.calendar=ALL-UNNAMED",
    "--add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED"
]

spark = SparkSession.builder \
    .config("spark.driver.extraJavaOptions", " ".join(java_17_options)) \
    .config("spark.executor.extraJavaOptions", " ".join(java_17_options)) \
    # ... other configs ...
    .getOrCreate()
```

### Running Tests Locally

For local testing with Java 17, you can use the provided `run_tests_java17.sh` script:

```bash
./run_tests_java17.sh --all
```

This script sets the necessary environment variables and runs the tests with the correct Java options.

### Manual Environment Setup

If you prefer to run tests manually, set the `JAVA_TOOL_OPTIONS` environment variable before running your tests:

```bash
export JAVA_TOOL_OPTIONS="--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED"
```

Then run your tests as usual:

```bash
source .venv/bin/activate
python -m pytest tests/ -v
```

### CI/CD Configuration

The Jenkins pipeline has been updated to include these Java 17 compatibility options in the environment variables.

## Alternative Solutions

If you continue to experience issues with Java 17, consider these alternatives:

1. Use Java 11 instead of Java 17 for running PySpark tests
2. Upgrade to a newer version of PySpark (3.3.0+) that has better Java 17 compatibility
3. Use Docker containers with a controlled Java environment for testing

## References

- [PySpark Java 17 Compatibility Issue](https://issues.apache.org/jira/browse/SPARK-37680)
- [Java Module System Documentation](https://openjdk.java.net/projects/jigsaw/quick-start)
- [PySpark Documentation](https://spark.apache.org/docs/3.2.4/api/python/index.html)
